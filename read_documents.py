#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档读取工具 - 用于读取课题申报相关文档
支持PDF、Word文档，并处理中文内容
"""

import os
import sys
from pathlib import Path
import pandas as pd

# 安装必要的库
def install_requirements():
    """安装必要的依赖库"""
    import subprocess
    
    packages = [
        'python-docx',
        'PyPDF2', 
        'pdfplumber',
        'openpyxl',
        'pandas'
    ]
    
    for package in packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            print(f"正在安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])

# 尝试安装依赖
try:
    install_requirements()
    import docx
    import PyPDF2
    import pdfplumber
    import openpyxl
except Exception as e:
    print(f"依赖安装失败: {e}")
    print("请手动安装: pip install python-docx PyPDF2 pdfplumber openpyxl pandas")

def read_docx(file_path):
    """读取Word文档"""
    try:
        doc = docx.Document(file_path)
        content = []
        
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                content.append(paragraph.text.strip())
        
        # 读取表格内容
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text.strip())
                if row_text:
                    content.append(" | ".join(row_text))
        
        return "\n".join(content)
    except Exception as e:
        return f"读取Word文档失败: {e}"

def read_pdf(file_path):
    """读取PDF文档"""
    try:
        content = []
        
        # 尝试使用pdfplumber（更好的中文支持）
        with pdfplumber.open(file_path) as pdf:
            for page in pdf.pages:
                text = page.extract_text()
                if text:
                    content.append(text)
        
        if not content:
            # 备用方案：使用PyPDF2
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text:
                        content.append(text)
        
        return "\n".join(content)
    except Exception as e:
        return f"读取PDF文档失败: {e}"

def read_excel(file_path):
    """读取Excel文档"""
    try:
        df = pd.read_excel(file_path)
        return df.to_string(index=False)
    except Exception as e:
        return f"读取Excel文档失败: {e}"

def read_document(file_path):
    """根据文件类型读取文档"""
    file_path = Path(file_path)
    
    if not file_path.exists():
        return f"文件不存在: {file_path}"
    
    suffix = file_path.suffix.lower()
    
    if suffix in ['.docx', '.doc']:
        return read_docx(file_path)
    elif suffix == '.pdf':
        return read_pdf(file_path)
    elif suffix in ['.xlsx', '.xls']:
        return read_excel(file_path)
    else:
        return f"不支持的文件格式: {suffix}"

def main():
    """主函数 - 读取所有相关文档"""

    # 设置输出编码为UTF-8
    import sys
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

    # 关键文档列表
    key_documents = [
        "附件1 2025年度汕头大学本科教学质量与教学改革工程项目立项指南.docx"
    ]

    print("=" * 80)
    print("课题申报文档分析")
    print("=" * 80)

    for doc_path in key_documents:
        try:
            print(f"\n{'='*60}")
            print(f"正在读取: {doc_path}")
            print(f"{'='*60}")

            content = read_document(doc_path)
            # 处理特殊字符
            content = content.replace('\u2022', '•').replace('\u2013', '-').replace('\u2014', '—')
            print(content)
            print("\n")
        except Exception as e:
            print(f"读取文档 {doc_path} 时出错: {e}")
            continue

if __name__ == "__main__":
    main()
