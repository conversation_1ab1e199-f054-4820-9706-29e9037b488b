<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.6.39">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">


<title>quarto-inputbeede105030d3f4f</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
</style>


<script src="汕头大学2025年教学改革项目申报方案_files/libs/clipboard/clipboard.min.js"></script>
<script src="汕头大学2025年教学改革项目申报方案_files/libs/quarto-html/quarto.js"></script>
<script src="汕头大学2025年教学改革项目申报方案_files/libs/quarto-html/popper.min.js"></script>
<script src="汕头大学2025年教学改革项目申报方案_files/libs/quarto-html/tippy.umd.min.js"></script>
<script src="汕头大学2025年教学改革项目申报方案_files/libs/quarto-html/anchor.min.js"></script>
<link href="汕头大学2025年教学改革项目申报方案_files/libs/quarto-html/tippy.css" rel="stylesheet">
<link href="汕头大学2025年教学改革项目申报方案_files/libs/quarto-html/quarto-syntax-highlighting-e26003cea8cd680ca0c55a263523d882.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="汕头大学2025年教学改革项目申报方案_files/libs/bootstrap/bootstrap.min.js"></script>
<link href="汕头大学2025年教学改革项目申报方案_files/libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="汕头大学2025年教学改革项目申报方案_files/libs/bootstrap/bootstrap-8a79a254b8e706d3c925cde0a310d4f0.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">


</head>

<body class="fullcontent">

<div id="quarto-content" class="page-columns page-rows-contents page-layout-article">

<main class="content" id="quarto-document-content"><header id="title-block-header" class="quarto-title-block"></header>




<section id="汕头大学2025年教学改革项目申报方案" class="level1">
<h1>汕头大学2025年教学改革项目申报方案</h1>
<section id="基于ai驱动的传媒内容制作课程的三个申报方向" class="level2">
<h2 class="anchored" data-anchor-id="基于ai驱动的传媒内容制作课程的三个申报方向">基于《AI驱动的传媒内容制作》课程的三个申报方向</h2>
<hr>
</section>
<section id="课题申报方向一四新建设专项---新文科背景下ai赋能传媒教育跨学科融合创新" class="level2">
<h2 class="anchored" data-anchor-id="课题申报方向一四新建设专项---新文科背景下ai赋能传媒教育跨学科融合创新">🎯 课题申报方向一：“四新”建设专项 - 新文科背景下AI赋能传媒教育跨学科融合创新</h2>
<section id="基本信息" class="level3">
<h3 class="anchored" data-anchor-id="基本信息">📋 基本信息</h3>
<ul>
<li><strong>项目类别</strong>：高等教育教学改革项目 - “四新”建设专项（新文科）</li>
<li><strong>建设周期</strong>：2年</li>
<li><strong>预期经费</strong>：0.8万元（校级），可申报省级3万元</li>
<li><strong>适用选题</strong>：对应”新文科重点围绕课程体系和教学内容重构”</li>
</ul>
</section>
<section id="项目概述" class="level3">
<h3 class="anchored" data-anchor-id="项目概述">🎯 项目概述</h3>
<p>以《AI驱动的传媒内容制作》课程为核心，构建”AI技术+传媒实践+人文素养+数据科学”四维融合的新文科教育模式，探索文理交叉、技术与人文并重的传媒人才培养新路径，培养具备AI素养、数据思维和人文底蕴的复合型传媒人才。</p>
</section>
<section id="研究内容" class="level3">
<h3 class="anchored" data-anchor-id="研究内容">📚 研究内容</h3>
<section id="跨学科课程体系重构" class="level4">
<h4 class="anchored" data-anchor-id="跨学科课程体系重构">1. <strong>跨学科课程体系重构</strong></h4>
<ul>
<li><strong>核心课程群建设</strong>：以《AI驱动的传媒内容制作》为核心，构建包含《数据新闻学》《计算传播学》《数字人文》《AI伦理与传媒法》的跨学科课程群</li>
<li><strong>知识图谱构建</strong>：建立传媒学+计算机科学+统计学+哲学伦理学的知识融合图谱</li>
<li><strong>能力矩阵设计</strong>：构建”技术应用能力+批判思维能力+创新实践能力+伦理判断能力”四维能力培养体系</li>
</ul>
</section>
<section id="教学内容与方法创新" class="level4">
<h4 class="anchored" data-anchor-id="教学内容与方法创新">2. <strong>教学内容与方法创新</strong></h4>
<ul>
<li><strong>案例库建设</strong>：开发100个融合AI技术与传媒实践的教学案例，涵盖新闻采编、内容创作、舆情分析、媒体运营等全流程</li>
<li><strong>实验教学创新</strong>：设计”AI+传统文化传播”实验项目，结合中华优秀传统文化，培养文化自信</li>
<li><strong>评价体系重构</strong>：建立多元化评价体系，包括技术应用评价、创新思维评价、伦理判断评价、团队协作评价</li>
</ul>
</section>
<section id="师资队伍跨界融合" class="level4">
<h4 class="anchored" data-anchor-id="师资队伍跨界融合">3. <strong>师资队伍跨界融合</strong></h4>
<ul>
<li><strong>双师制度</strong>：建立传媒教师+技术专家的双师教学模式</li>
<li><strong>企业导师引入</strong>：邀请AI公司、媒体机构专家参与课程设计和教学实施</li>
<li><strong>教师能力提升</strong>：开展AI技术培训，提升传媒教师的技术应用能力</li>
</ul>
</section>
</section>
<section id="创新特色" class="level3">
<h3 class="anchored" data-anchor-id="创新特色">🔬 创新特色</h3>
<ul>
<li><strong>跨界融合性</strong>：打破学科壁垒，实现文理深度融合</li>
<li><strong>技术前沿性</strong>：紧跟AI大模型发展，及时更新教学内容</li>
<li><strong>人文关怀性</strong>：强调技术与人文的平衡，培养有温度的AI应用者</li>
<li><strong>实践导向性</strong>：以真实传媒项目为载体，提升实战能力</li>
</ul>
</section>
<section id="预期成果" class="level3">
<h3 class="anchored" data-anchor-id="预期成果">📊 预期成果</h3>
<ul>
<li>建成1套完整的跨学科课程体系</li>
<li>开发100个AI+传媒融合教学案例</li>
<li>培养具备跨学科素养的复合型人才</li>
<li>形成可推广的新文科教育模式</li>
</ul>
<hr>
</section>
</section>
<section id="课题申报方向二四新建设专项---基于大模型的智能传媒内容生产教学工厂建设" class="level2">
<h2 class="anchored" data-anchor-id="课题申报方向二四新建设专项---基于大模型的智能传媒内容生产教学工厂建设">🎯 课题申报方向二：“四新”建设专项 - 基于大模型的智能传媒内容生产教学工厂建设</h2>
<section id="基本信息-1" class="level3">
<h3 class="anchored" data-anchor-id="基本信息-1">📋 基本信息</h3>
<ul>
<li><strong>项目类别</strong>：高等教育教学改革项目 - “四新”建设专项（新文科）</li>
<li><strong>建设周期</strong>：2年</li>
<li><strong>预期经费</strong>：0.8万元（校级），可申报省级3万元</li>
<li><strong>适用选题</strong>：对应”以多种形式积极探索并实践’四新’人才培养”</li>
</ul>
</section>
<section id="项目概述-1" class="level3">
<h3 class="anchored" data-anchor-id="项目概述-1">🎯 项目概述</h3>
<p>构建集”教学+实训+创新+孵化”于一体的智能传媒内容生产教学工厂，以《AI驱动的传媒内容制作》课程为基础，建设真实的AI赋能传媒生产环境，实现”在做中学、在学中创、在创中研”的新文科人才培养模式。</p>
</section>
<section id="研究内容-1" class="level3">
<h3 class="anchored" data-anchor-id="研究内容-1">📚 研究内容</h3>
<section id="教学工厂平台建设" class="level4">
<h4 class="anchored" data-anchor-id="教学工厂平台建设">1. <strong>教学工厂平台建设</strong></h4>
<ul>
<li><strong>硬件环境</strong>：建设配备主流AI大模型平台（腾讯元宝、豆包、文心一言等）的智能工作站</li>
<li><strong>软件生态</strong>：集成内容创作、数据分析、媒体发布、效果监测的全流程软件工具链</li>
<li><strong>实训场景</strong>：模拟真实媒体机构工作环境，设置新闻编辑部、创意工作室、数据分析中心等功能区域</li>
</ul>
</section>
<section id="产教融合机制创新" class="level4">
<h4 class="anchored" data-anchor-id="产教融合机制创新">2. <strong>产教融合机制创新</strong></h4>
<ul>
<li><strong>企业项目引入</strong>：与腾讯、字节跳动、新华社等机构合作，引入真实项目作为教学案例</li>
<li><strong>行业标准对接</strong>：按照行业标准设计教学流程，培养符合市场需求的人才</li>
<li><strong>成果转化机制</strong>：建立学生作品商业化转化通道，实现教学成果的市场价值</li>
</ul>
</section>
<section id="智能化教学支持系统" class="level4">
<h4 class="anchored" data-anchor-id="智能化教学支持系统">3. <strong>智能化教学支持系统</strong></h4>
<ul>
<li><strong>AI教学助手</strong>：开发基于大模型的个性化学习指导系统</li>
<li><strong>智能评价系统</strong>：构建AI辅助的作品评价和反馈机制</li>
<li><strong>数据驱动优化</strong>：通过学习数据分析，持续优化教学内容和方法</li>
</ul>
</section>
</section>
<section id="创新特色-1" class="level3">
<h3 class="anchored" data-anchor-id="创新特色-1">🔬 创新特色</h3>
<ul>
<li><strong>工厂化教学</strong>：模拟真实生产环境，提升实战能力</li>
<li><strong>智能化支持</strong>：全程AI辅助，提高学习效率</li>
<li><strong>产业化导向</strong>：直接对接市场需求，增强就业竞争力</li>
<li><strong>创新性孵化</strong>：为学生创新创业提供平台支持</li>
</ul>
</section>
<section id="预期成果-1" class="level3">
<h3 class="anchored" data-anchor-id="预期成果-1">📊 预期成果</h3>
<ul>
<li>建成1个智能传媒内容生产教学工厂</li>
<li>开发智能化教学支持系统</li>
<li>建立产教融合的长效机制</li>
<li>培养一批创新创业人才</li>
</ul>
<hr>
</section>
</section>
<section id="课题申报方向三项目式教学改革专项---ai传媒内容生产全流程项目式教学改革" class="level2">
<h2 class="anchored" data-anchor-id="课题申报方向三项目式教学改革专项---ai传媒内容生产全流程项目式教学改革">🎯 课题申报方向三：项目式教学改革专项 - “AI+传媒内容生产”全流程项目式教学改革</h2>
<section id="基本信息-2" class="level3">
<h3 class="anchored" data-anchor-id="基本信息-2">📋 基本信息</h3>
<ul>
<li><strong>项目类别</strong>：高等教育教学改革项目 - 项目式教学改革专项</li>
<li><strong>建设周期</strong>：2年</li>
<li><strong>预期经费</strong>：0.8万元（校级），可申报省级3万元</li>
<li><strong>适用选题</strong>：对应广东省选题第7号”项目式教学改革专项”</li>
</ul>
</section>
<section id="项目概述-2" class="level3">
<h3 class="anchored" data-anchor-id="项目概述-2">🎯 项目概述</h3>
<p>以《AI驱动的传媒内容制作》课程为基础，构建”真实项目驱动、AI技术赋能、全流程实践、多元协同创新”的项目式教学模式，通过播客制作、社交媒体运营、数据新闻、短视频创作等综合性项目，实现AI技术与传媒实践的深度融合，培养具备项目管理能力和创新思维的新时代传媒人才。</p>
</section>
<section id="研究内容-2" class="level3">
<h3 class="anchored" data-anchor-id="研究内容-2">📚 研究内容</h3>
<section id="项目式教学体系设计与实施" class="level4">
<h4 class="anchored" data-anchor-id="项目式教学体系设计与实施">1. <strong>项目式教学体系设计与实施</strong></h4>
<section id="分层递进的项目体系构建" class="level5">
<h5 class="anchored" data-anchor-id="分层递进的项目体系构建">1.1 <strong>分层递进的项目体系构建</strong></h5>
<ul>
<li><strong>基础项目层（第1-4周）</strong>：
<ul>
<li>项目1：AI辅助新闻写作（基于真实新闻事件，运用提示词工程完成新闻稿件）</li>
<li>项目2：智能信息搜集与核查（针对热点话题，使用多个AI平台进行信息收集和事实核查）</li>
<li>项目3：内容摘要与数据提取（处理长篇报告、会议纪要等，生成结构化摘要）</li>
</ul></li>
<li><strong>进阶项目层（第5-10周）</strong>：
<ul>
<li>项目4：AI驱动的选题策划（结合热点趋势，生成创新选题方案）</li>
<li>项目5：多媒体内容创作（文字+图片+音频的综合内容制作）</li>
<li>项目6：社交媒体内容矩阵（为特定品牌设计一周的社媒内容计划）</li>
</ul></li>
<li><strong>综合项目层（第11-16周）</strong>：
<ul>
<li>项目7：播客节目制作（从策划到制作到推广的全流程项目）</li>
<li>项目8：数据新闻作品（结合公开数据，制作可视化新闻作品）</li>
<li>项目9：模拟媒体账号运营（运营一个真实的社交媒体账号一个月）</li>
<li>项目10：AI伦理案例分析（分析AI在传媒应用中的伦理问题并提出解决方案）</li>
</ul></li>
</ul>
</section>
<section id="项目实施流程标准化" class="level5">
<h5 class="anchored" data-anchor-id="项目实施流程标准化">1.2 <strong>项目实施流程标准化</strong></h5>
<ul>
<li><strong>项目启动阶段</strong>：需求分析→目标设定→团队组建→资源配置</li>
<li><strong>项目执行阶段</strong>：任务分解→进度管理→质量控制→风险应对</li>
<li><strong>项目交付阶段</strong>：成果展示→同行评议→反思总结→经验提炼</li>
<li><strong>项目评估阶段</strong>：多维评价→改进建议→知识沉淀→成果转化</li>
</ul>
</section>
</section>
<section id="ai工具深度集成与应用创新" class="level4">
<h4 class="anchored" data-anchor-id="ai工具深度集成与应用创新">2. <strong>AI工具深度集成与应用创新</strong></h4>
<section id="多平台ai工具矩阵" class="level5">
<h5 class="anchored" data-anchor-id="多平台ai工具矩阵">2.1 <strong>多平台AI工具矩阵</strong></h5>
<ul>
<li><strong>文本生成类</strong>：腾讯元宝、豆包、文心一言、通义千问、Kimi、智谱清言</li>
<li><strong>图像生成类</strong>：Midjourney、DALL-E、Stable Diffusion</li>
<li><strong>音频处理类</strong>：剪映、Adobe Audition AI功能</li>
<li><strong>数据分析类</strong>：Python + ChatGPT Code Interpreter、Tableau AI</li>
</ul>
</section>
<section id="提示词工程深度应用" class="level5">
<h5 class="anchored" data-anchor-id="提示词工程深度应用">2.2 <strong>提示词工程深度应用</strong></h5>
<ul>
<li><strong>基础提示词库建设</strong>：建立涵盖新闻写作、创意策划、数据分析等领域的标准提示词库</li>
<li><strong>高级提示技巧训练</strong>：Few-Shot学习、角色扮演、思维链推理在传媒项目中的应用</li>
<li><strong>个性化提示词开发</strong>：指导学生根据项目需求开发专属提示词模板</li>
</ul>
</section>
<section id="ai伦理与职业操守实践" class="level5">
<h5 class="anchored" data-anchor-id="ai伦理与职业操守实践">2.3 <strong>AI伦理与职业操守实践</strong></h5>
<ul>
<li><strong>伦理决策框架</strong>：建立AI应用的伦理判断标准和决策流程</li>
<li><strong>案例分析实践</strong>：通过真实案例分析，培养学生的伦理敏感性</li>
<li><strong>职业操守培养</strong>：结合中华优秀传统文化，强化媒体人的社会责任感</li>
</ul>
</section>
</section>
<section id="协同创新机制探索与实践" class="level4">
<h4 class="anchored" data-anchor-id="协同创新机制探索与实践">3. <strong>协同创新机制探索与实践</strong></h4>
<section id="师生协同创新模式" class="level5">
<h5 class="anchored" data-anchor-id="师生协同创新模式">3.1 <strong>师生协同创新模式</strong></h5>
<ul>
<li><strong>导师制项目指导</strong>：每个项目配备专业导师和技术导师</li>
<li><strong>学生项目经理制</strong>：培养学生的项目管理和团队领导能力</li>
<li><strong>师生共创机制</strong>：鼓励师生共同申报创新项目和参加竞赛</li>
</ul>
</section>
<section id="跨专业团队协作" class="level5">
<h5 class="anchored" data-anchor-id="跨专业团队协作">3.2 <strong>跨专业团队协作</strong></h5>
<ul>
<li><strong>新闻+计算机+设计</strong>：组建跨专业项目团队，发挥各专业优势</li>
<li><strong>文科+理科融合</strong>：探索文理交叉的项目合作模式</li>
<li><strong>本科+研究生协作</strong>：建立本研协同的项目团队结构</li>
</ul>
</section>
<section id="产学研一体化平台" class="level5">
<h5 class="anchored" data-anchor-id="产学研一体化平台">3.3 <strong>产学研一体化平台</strong></h5>
<ul>
<li><strong>企业项目引入</strong>：与腾讯、字节跳动、新华社等建立项目合作关系</li>
<li><strong>行业导师参与</strong>：邀请行业专家担任项目外部导师</li>
<li><strong>成果转化机制</strong>：建立优秀项目的商业化转化通道</li>
</ul>
</section>
</section>
<section id="评价体系创新与质量保障" class="level4">
<h4 class="anchored" data-anchor-id="评价体系创新与质量保障">4. <strong>评价体系创新与质量保障</strong></h4>
<section id="多元化评价指标体系" class="level5">
<h5 class="anchored" data-anchor-id="多元化评价指标体系">4.1 <strong>多元化评价指标体系</strong></h5>
<ul>
<li><strong>过程评价（40%）</strong>：
<ul>
<li>项目参与度（10%）</li>
<li>团队协作能力（10%）</li>
<li>创新思维表现（10%）</li>
<li>问题解决能力（10%）</li>
</ul></li>
<li><strong>成果评价（40%）</strong>：
<ul>
<li>作品质量（20%）</li>
<li>技术应用水平（10%）</li>
<li>创意创新程度（10%）</li>
</ul></li>
<li><strong>反思评价（20%）</strong>：
<ul>
<li>学习反思报告（10%）</li>
<li>同伴互评（5%）</li>
<li>自我评价（5%）</li>
</ul></li>
</ul>
</section>
<section id="智能化评价工具" class="level5">
<h5 class="anchored" data-anchor-id="智能化评价工具">4.2 <strong>智能化评价工具</strong></h5>
<ul>
<li><strong>AI辅助评分系统</strong>：开发基于大模型的作品评价工具</li>
<li><strong>数据驱动分析</strong>：通过学习数据分析，优化教学设计</li>
<li><strong>个性化反馈机制</strong>：为每个学生提供个性化的改进建议</li>
</ul>
</section>
</section>
<section id="竞赛导向与成果转化" class="level4">
<h4 class="anchored" data-anchor-id="竞赛导向与成果转化">5. <strong>竞赛导向与成果转化</strong></h4>
<section id="竞赛项目对接" class="level5">
<h5 class="anchored" data-anchor-id="竞赛项目对接">5.1 <strong>竞赛项目对接</strong></h5>
<ul>
<li><strong>全国大学生新媒体创意大赛</strong></li>
<li><strong>中国大学生计算机设计大赛</strong></li>
<li><strong>全国大学生数字媒体科技作品及创意竞赛</strong></li>
<li><strong>“挑战杯”全国大学生课外学术科技作品竞赛</strong></li>
</ul>
</section>
<section id="成果孵化机制" class="level5">
<h5 class="anchored" data-anchor-id="成果孵化机制">5.2 <strong>成果孵化机制</strong></h5>
<ul>
<li><strong>优秀作品展示平台</strong>：建设线上线下展示空间</li>
<li><strong>创业项目孵化</strong>：为有商业价值的项目提供孵化支持</li>
<li><strong>学术成果转化</strong>：鼓励将项目成果转化为学术论文和专利</li>
</ul>
</section>
</section>
</section>
<section id="创新特色-2" class="level3">
<h3 class="anchored" data-anchor-id="创新特色-2">🔬 创新特色</h3>
<section id="真实性与实战性" class="level4">
<h4 class="anchored" data-anchor-id="真实性与实战性">1. <strong>真实性与实战性</strong></h4>
<ul>
<li>所有项目均基于真实传媒工作场景设计</li>
<li>与行业标准和市场需求紧密对接</li>
<li>学生在真实环境中锻炼实战能力</li>
</ul>
</section>
<section id="系统性与完整性" class="level4">
<h4 class="anchored" data-anchor-id="系统性与完整性">2. <strong>系统性与完整性</strong></h4>
<ul>
<li>覆盖传媒内容生产的全流程环节</li>
<li>从基础技能到综合应用的递进式设计</li>
<li>理论学习与实践应用的有机结合</li>
</ul>
</section>
<section id="前沿性与适应性" class="level4">
<h4 class="anchored" data-anchor-id="前沿性与适应性">3. <strong>前沿性与适应性</strong></h4>
<ul>
<li>紧跟AI技术发展趋势，及时更新工具应用</li>
<li>建立技术更新的快速响应机制</li>
<li>培养学生的技术适应能力和终身学习能力</li>
</ul>
</section>
<section id="协同性与开放性" class="level4">
<h4 class="anchored" data-anchor-id="协同性与开放性">4. <strong>协同性与开放性</strong></h4>
<ul>
<li>多主体参与的协同育人模式</li>
<li>跨专业、跨学科的开放式合作</li>
<li>校内外资源的有效整合利用</li>
</ul>
</section>
</section>
<section id="预期成果-2" class="level3">
<h3 class="anchored" data-anchor-id="预期成果-2">📊 预期成果</h3>
<section id="教学体系成果" class="level4">
<h4 class="anchored" data-anchor-id="教学体系成果">1. <strong>教学体系成果</strong></h4>
<ul>
<li>建立1套完整的AI+传媒项目式教学体系</li>
<li>开发10-15个标准化项目案例库</li>
<li>形成可复制、可推广的项目式教学模式</li>
<li>建设AI+传媒教学资源库</li>
</ul>
</section>
<section id="人才培养成果" class="level4">
<h4 class="anchored" data-anchor-id="人才培养成果">2. <strong>人才培养成果</strong></h4>
<ul>
<li>培养学生AI应用能力，技术应用水平提升80%</li>
<li>项目作品获奖率提升50%以上</li>
<li>学生就业竞争力显著增强</li>
<li>培养一批具备项目管理能力的复合型人才</li>
</ul>
</section>
<section id="学术研究成果" class="level4">
<h4 class="anchored" data-anchor-id="学术研究成果">3. <strong>学术研究成果</strong></h4>
<ul>
<li>发表教改论文8-10篇</li>
<li>申报教学成果奖1-2项</li>
<li>形成项目式教学改革的理论体系</li>
<li>建立AI时代传媒教育的新标准</li>
</ul>
</section>
<section id="社会服务成果" class="level4">
<h4 class="anchored" data-anchor-id="社会服务成果">4. <strong>社会服务成果</strong></h4>
<ul>
<li>为行业培养急需的AI+传媒复合型人才</li>
<li>推动传媒行业的数字化转型</li>
<li>服务地方经济社会发展需求</li>
<li>提升学校社会影响力和美誉度</li>
</ul>
</section>
</section>
<section id="实施保障" class="level3">
<h3 class="anchored" data-anchor-id="实施保障">📋 实施保障</h3>
<section id="组织保障" class="level4">
<h4 class="anchored" data-anchor-id="组织保障">1. <strong>组织保障</strong></h4>
<ul>
<li>成立项目式教学改革领导小组</li>
<li>建立跨学院协调机制</li>
<li>设立专项资金支持</li>
</ul>
</section>
<section id="师资保障" class="level4">
<h4 class="anchored" data-anchor-id="师资保障">2. <strong>师资保障</strong></h4>
<ul>
<li>组建高水平教学团队</li>
<li>开展教师AI技术培训</li>
<li>引入行业专家参与教学</li>
</ul>
</section>
<section id="技术保障" class="level4">
<h4 class="anchored" data-anchor-id="技术保障">3. <strong>技术保障</strong></h4>
<ul>
<li>建设AI教学实验室</li>
<li>购置必要的软硬件设备</li>
<li>建立技术支持服务体系</li>
</ul>
</section>
<section id="制度保障" class="level4">
<h4 class="anchored" data-anchor-id="制度保障">4. <strong>制度保障</strong></h4>
<ul>
<li>制定项目式教学管理制度</li>
<li>建立质量监控体系</li>
<li>完善激励评价机制</li>
</ul>
<hr>
</section>
</section>
</section>
<section id="三个课题申报方向总结与建议" class="level2">
<h2 class="anchored" data-anchor-id="三个课题申报方向总结与建议">📝 三个课题申报方向总结与建议</h2>
<section id="优先推荐顺序" class="level3">
<h3 class="anchored" data-anchor-id="优先推荐顺序">🎯 <strong>优先推荐顺序</strong></h3>
<section id="首选项目式教学改革专项扩充版" class="level4">
<h4 class="anchored" data-anchor-id="首选项目式教学改革专项扩充版">1. <strong>首选：项目式教学改革专项</strong>（扩充版）</h4>
<p><strong>推荐理由</strong>： - ✅ <strong>实践基础扎实</strong>：完全基于现有《AI驱动的传媒内容制作》课程 - ✅ <strong>可操作性强</strong>：10个具体项目设计，实施路径清晰 - ✅ <strong>成果可预期</strong>：容易产出教学成果和获奖作品 - ✅ <strong>符合政策导向</strong>：完全契合”项目式教学改革专项”要求</p>
</section>
<section id="次选新文科跨学科融合创新" class="level4">
<h4 class="anchored" data-anchor-id="次选新文科跨学科融合创新">2. <strong>次选：新文科跨学科融合创新</strong></h4>
<p><strong>推荐理由</strong>： - ✅ <strong>创新性突出</strong>：AI+传媒+人文的跨界融合 - ✅ <strong>符合”四新”要求</strong>：体现新文科建设核心理念 - ✅ <strong>发展前景好</strong>：代表未来教育发展方向</p>
</section>
<section id="备选智能传媒教学工厂" class="level4">
<h4 class="anchored" data-anchor-id="备选智能传媒教学工厂">3. <strong>备选：智能传媒教学工厂</strong></h4>
<p><strong>推荐理由</strong>： - ✅ <strong>特色鲜明</strong>：工厂化教学模式创新 - ✅ <strong>产教融合深度</strong>：与行业紧密对接 - ✅ <strong>投入产出比高</strong>：可持续发展性强</p>
</section>
</section>
<section id="申报策略建议" class="level3">
<h3 class="anchored" data-anchor-id="申报策略建议">📊 <strong>申报策略建议</strong></h3>
<section id="短期策略2025年" class="level4">
<h4 class="anchored" data-anchor-id="短期策略2025年"><strong>短期策略（2025年）</strong></h4>
<ol type="1">
<li><strong>重点申报</strong>：项目式教学改革专项（校级）</li>
<li><strong>同步准备</strong>：新文科跨学科融合创新（省级）</li>
</ol>
</section>
<section id="中期策略2026年" class="level4">
<h4 class="anchored" data-anchor-id="中期策略2026年"><strong>中期策略（2026年）</strong></h4>
<ol type="1">
<li><strong>成果转化</strong>：将项目式教学成果申报教学成果奖</li>
<li><strong>扩展申报</strong>：智能传媒教学工厂（省级重点项目）</li>
</ol>
</section>
<section id="长期策略2027-2028年" class="level4">
<h4 class="anchored" data-anchor-id="长期策略2027-2028年"><strong>长期策略（2027-2028年）</strong></h4>
<ol type="1">
<li><strong>整合提升</strong>：三个项目整合申报国家级教学成果奖</li>
<li><strong>模式推广</strong>：形成可复制的AI+传媒教育模式</li>
</ol>
</section>
</section>
<section id="申报材料完善建议" class="level3">
<h3 class="anchored" data-anchor-id="申报材料完善建议">🔧 <strong>申报材料完善建议</strong></h3>
<section id="共同优化点" class="level4">
<h4 class="anchored" data-anchor-id="共同优化点"><strong>共同优化点</strong></h4>
<ol type="1">
<li><strong>突出现有基础</strong>：充分展示《AI驱动的传媒内容制作》课程的成功实践</li>
<li><strong>强化创新特色</strong>：突出AI技术与传媒教育深度融合的独特性</li>
<li><strong>明确预期成果</strong>：设定可量化、可考核的具体目标</li>
<li><strong>完善实施方案</strong>：提供详细的时间节点和里程碑计划</li>
</ol>
</section>
<section id="差异化策略" class="level4">
<h4 class="anchored" data-anchor-id="差异化策略"><strong>差异化策略</strong></h4>
<ul>
<li><strong>项目式教学</strong>：重点突出实践性和可操作性</li>
<li><strong>新文科建设</strong>：重点突出跨学科融合和理论创新</li>
<li><strong>教学工厂</strong>：重点突出产教融合和平台建设</li>
</ul>
<hr>
</section>
</section>
</section>
<section id="附录基础课程信息" class="level2">
<h2 class="anchored" data-anchor-id="附录基础课程信息">📋 附录：基础课程信息</h2>
<section id="ai驱动的传媒内容制作课程概况" class="level3">
<h3 class="anchored" data-anchor-id="ai驱动的传媒内容制作课程概况">《AI驱动的传媒内容制作》课程概况</h3>
<ul>
<li><strong>课程代码</strong>：JOU2155A</li>
<li><strong>学分</strong>：2学分</li>
<li><strong>课时</strong>：32课时</li>
<li><strong>开课单位</strong>：长江新闻与传播学院</li>
<li><strong>课程负责人</strong>：马琪昌</li>
</ul>
</section>
<section id="课程特色" class="level3">
<h3 class="anchored" data-anchor-id="课程特色">课程特色</h3>
<ul>
<li>采用ASK教学法（Attitude-Skill-Knowledge）</li>
<li>涵盖5个知识单元：AI与LLM基础、提示词工程、核心应用场景、进阶技巧、综合应用</li>
<li>集成腾讯元宝、豆包、文心一言等主流AI平台</li>
<li>期末项目包括播客制作、社交媒体运营、数据新闻等实践项目</li>
<li>强调AI伦理与职业操守培养</li>
</ul>
<hr>
<p><em>本申报方案基于汕头大学2025年度教学质量与教学改革工程项目立项指南制定，充分结合现有《AI驱动的传媒内容制作》课程实践基础，旨在推动AI技术与传媒教育的深度融合，培养新时代复合型传媒人才。</em></p>
</section>
</section>
</section>

</main>
<!-- /main column -->
<script id="quarto-html-after-body" type="application/javascript">
window.document.addEventListener("DOMContentLoaded", function (event) {
  const toggleBodyColorMode = (bsSheetEl) => {
    const mode = bsSheetEl.getAttribute("data-mode");
    const bodyEl = window.document.querySelector("body");
    if (mode === "dark") {
      bodyEl.classList.add("quarto-dark");
      bodyEl.classList.remove("quarto-light");
    } else {
      bodyEl.classList.add("quarto-light");
      bodyEl.classList.remove("quarto-dark");
    }
  }
  const toggleBodyColorPrimary = () => {
    const bsSheetEl = window.document.querySelector("link#quarto-bootstrap");
    if (bsSheetEl) {
      toggleBodyColorMode(bsSheetEl);
    }
  }
  toggleBodyColorPrimary();  
  const icon = "";
  const anchorJS = new window.AnchorJS();
  anchorJS.options = {
    placement: 'right',
    icon: icon
  };
  anchorJS.add('.anchored');
  const isCodeAnnotation = (el) => {
    for (const clz of el.classList) {
      if (clz.startsWith('code-annotation-')) {                     
        return true;
      }
    }
    return false;
  }
  const onCopySuccess = function(e) {
    // button target
    const button = e.trigger;
    // don't keep focus
    button.blur();
    // flash "checked"
    button.classList.add('code-copy-button-checked');
    var currentTitle = button.getAttribute("title");
    button.setAttribute("title", "Copied!");
    let tooltip;
    if (window.bootstrap) {
      button.setAttribute("data-bs-toggle", "tooltip");
      button.setAttribute("data-bs-placement", "left");
      button.setAttribute("data-bs-title", "Copied!");
      tooltip = new bootstrap.Tooltip(button, 
        { trigger: "manual", 
          customClass: "code-copy-button-tooltip",
          offset: [0, -8]});
      tooltip.show();    
    }
    setTimeout(function() {
      if (tooltip) {
        tooltip.hide();
        button.removeAttribute("data-bs-title");
        button.removeAttribute("data-bs-toggle");
        button.removeAttribute("data-bs-placement");
      }
      button.setAttribute("title", currentTitle);
      button.classList.remove('code-copy-button-checked');
    }, 1000);
    // clear code selection
    e.clearSelection();
  }
  const getTextToCopy = function(trigger) {
      const codeEl = trigger.previousElementSibling.cloneNode(true);
      for (const childEl of codeEl.children) {
        if (isCodeAnnotation(childEl)) {
          childEl.remove();
        }
      }
      return codeEl.innerText;
  }
  const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
    text: getTextToCopy
  });
  clipboard.on('success', onCopySuccess);
  if (window.document.getElementById('quarto-embedded-source-code-modal')) {
    const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
      text: getTextToCopy,
      container: window.document.getElementById('quarto-embedded-source-code-modal')
    });
    clipboardModal.on('success', onCopySuccess);
  }
    var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
    var mailtoRegex = new RegExp(/^mailto:/);
      var filterRegex = new RegExp('/' + window.location.host + '/');
    var isInternal = (href) => {
        return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
    }
    // Inspect non-navigation links and adorn them if external
 	var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
    for (var i=0; i<links.length; i++) {
      const link = links[i];
      if (!isInternal(link.href)) {
        // undo the damage that might have been done by quarto-nav.js in the case of
        // links that we want to consider external
        if (link.dataset.originalHref !== undefined) {
          link.href = link.dataset.originalHref;
        }
      }
    }
  function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
    const config = {
      allowHTML: true,
      maxWidth: 500,
      delay: 100,
      arrow: false,
      appendTo: function(el) {
          return el.parentElement;
      },
      interactive: true,
      interactiveBorder: 10,
      theme: 'quarto',
      placement: 'bottom-start',
    };
    if (contentFn) {
      config.content = contentFn;
    }
    if (onTriggerFn) {
      config.onTrigger = onTriggerFn;
    }
    if (onUntriggerFn) {
      config.onUntrigger = onUntriggerFn;
    }
    window.tippy(el, config); 
  }
  const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
  for (var i=0; i<noterefs.length; i++) {
    const ref = noterefs[i];
    tippyHover(ref, function() {
      // use id or data attribute instead here
      let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
      try { href = new URL(href).hash; } catch {}
      const id = href.replace(/^#\/?/, "");
      const note = window.document.getElementById(id);
      if (note) {
        return note.innerHTML;
      } else {
        return "";
      }
    });
  }
  const xrefs = window.document.querySelectorAll('a.quarto-xref');
  const processXRef = (id, note) => {
    // Strip column container classes
    const stripColumnClz = (el) => {
      el.classList.remove("page-full", "page-columns");
      if (el.children) {
        for (const child of el.children) {
          stripColumnClz(child);
        }
      }
    }
    stripColumnClz(note)
    if (id === null || id.startsWith('sec-')) {
      // Special case sections, only their first couple elements
      const container = document.createElement("div");
      if (note.children && note.children.length > 2) {
        container.appendChild(note.children[0].cloneNode(true));
        for (let i = 1; i < note.children.length; i++) {
          const child = note.children[i];
          if (child.tagName === "P" && child.innerText === "") {
            continue;
          } else {
            container.appendChild(child.cloneNode(true));
            break;
          }
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(container);
        }
        return container.innerHTML
      } else {
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        return note.innerHTML;
      }
    } else {
      // Remove any anchor links if they are present
      const anchorLink = note.querySelector('a.anchorjs-link');
      if (anchorLink) {
        anchorLink.remove();
      }
      if (window.Quarto?.typesetMath) {
        window.Quarto.typesetMath(note);
      }
      if (note.classList.contains("callout")) {
        return note.outerHTML;
      } else {
        return note.innerHTML;
      }
    }
  }
  for (var i=0; i<xrefs.length; i++) {
    const xref = xrefs[i];
    tippyHover(xref, undefined, function(instance) {
      instance.disable();
      let url = xref.getAttribute('href');
      let hash = undefined; 
      if (url.startsWith('#')) {
        hash = url;
      } else {
        try { hash = new URL(url).hash; } catch {}
      }
      if (hash) {
        const id = hash.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note !== null) {
          try {
            const html = processXRef(id, note.cloneNode(true));
            instance.setContent(html);
          } finally {
            instance.enable();
            instance.show();
          }
        } else {
          // See if we can fetch this
          fetch(url.split('#')[0])
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.getElementById(id);
            if (note !== null) {
              const html = processXRef(id, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      } else {
        // See if we can fetch a full url (with no hash to target)
        // This is a special case and we should probably do some content thinning / targeting
        fetch(url)
        .then(res => res.text())
        .then(html => {
          const parser = new DOMParser();
          const htmlDoc = parser.parseFromString(html, "text/html");
          const note = htmlDoc.querySelector('main.content');
          if (note !== null) {
            // This should only happen for chapter cross references
            // (since there is no id in the URL)
            // remove the first header
            if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
              note.children[0].remove();
            }
            const html = processXRef(null, note);
            instance.setContent(html);
          } 
        }).finally(() => {
          instance.enable();
          instance.show();
        });
      }
    }, function(instance) {
    });
  }
      let selectedAnnoteEl;
      const selectorForAnnotation = ( cell, annotation) => {
        let cellAttr = 'data-code-cell="' + cell + '"';
        let lineAttr = 'data-code-annotation="' +  annotation + '"';
        const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
        return selector;
      }
      const selectCodeLines = (annoteEl) => {
        const doc = window.document;
        const targetCell = annoteEl.getAttribute("data-target-cell");
        const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
        const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
        const lines = annoteSpan.getAttribute("data-code-lines").split(",");
        const lineIds = lines.map((line) => {
          return targetCell + "-" + line;
        })
        let top = null;
        let height = null;
        let parent = null;
        if (lineIds.length > 0) {
            //compute the position of the single el (top and bottom and make a div)
            const el = window.document.getElementById(lineIds[0]);
            top = el.offsetTop;
            height = el.offsetHeight;
            parent = el.parentElement.parentElement;
          if (lineIds.length > 1) {
            const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
            const bottom = lastEl.offsetTop + lastEl.offsetHeight;
            height = bottom - top;
          }
          if (top !== null && height !== null && parent !== null) {
            // cook up a div (if necessary) and position it 
            let div = window.document.getElementById("code-annotation-line-highlight");
            if (div === null) {
              div = window.document.createElement("div");
              div.setAttribute("id", "code-annotation-line-highlight");
              div.style.position = 'absolute';
              parent.appendChild(div);
            }
            div.style.top = top - 2 + "px";
            div.style.height = height + 4 + "px";
            div.style.left = 0;
            let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
            if (gutterDiv === null) {
              gutterDiv = window.document.createElement("div");
              gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
              gutterDiv.style.position = 'absolute';
              const codeCell = window.document.getElementById(targetCell);
              const gutter = codeCell.querySelector('.code-annotation-gutter');
              gutter.appendChild(gutterDiv);
            }
            gutterDiv.style.top = top - 2 + "px";
            gutterDiv.style.height = height + 4 + "px";
          }
          selectedAnnoteEl = annoteEl;
        }
      };
      const unselectCodeLines = () => {
        const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
        elementsIds.forEach((elId) => {
          const div = window.document.getElementById(elId);
          if (div) {
            div.remove();
          }
        });
        selectedAnnoteEl = undefined;
      };
        // Handle positioning of the toggle
    window.addEventListener(
      "resize",
      throttle(() => {
        elRect = undefined;
        if (selectedAnnoteEl) {
          selectCodeLines(selectedAnnoteEl);
        }
      }, 10)
    );
    function throttle(fn, ms) {
    let throttle = false;
    let timer;
      return (...args) => {
        if(!throttle) { // first call gets through
            fn.apply(this, args);
            throttle = true;
        } else { // all the others get throttled
            if(timer) clearTimeout(timer); // cancel #2
            timer = setTimeout(() => {
              fn.apply(this, args);
              timer = throttle = false;
            }, ms);
        }
      };
    }
      // Attach click handler to the DT
      const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
      for (const annoteDlNode of annoteDls) {
        annoteDlNode.addEventListener('click', (event) => {
          const clickedEl = event.target;
          if (clickedEl !== selectedAnnoteEl) {
            unselectCodeLines();
            const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
            if (activeEl) {
              activeEl.classList.remove('code-annotation-active');
            }
            selectCodeLines(clickedEl);
            clickedEl.classList.add('code-annotation-active');
          } else {
            // Unselect the line
            unselectCodeLines();
            clickedEl.classList.remove('code-annotation-active');
          }
        });
      }
  const findCites = (el) => {
    const parentEl = el.parentElement;
    if (parentEl) {
      const cites = parentEl.dataset.cites;
      if (cites) {
        return {
          el,
          cites: cites.split(' ')
        };
      } else {
        return findCites(el.parentElement)
      }
    } else {
      return undefined;
    }
  };
  var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
  for (var i=0; i<bibliorefs.length; i++) {
    const ref = bibliorefs[i];
    const citeInfo = findCites(ref);
    if (citeInfo) {
      tippyHover(citeInfo.el, function() {
        var popup = window.document.createElement('div');
        citeInfo.cites.forEach(function(cite) {
          var citeDiv = window.document.createElement('div');
          citeDiv.classList.add('hanging-indent');
          citeDiv.classList.add('csl-entry');
          var biblioDiv = window.document.getElementById('ref-' + cite);
          if (biblioDiv) {
            citeDiv.innerHTML = biblioDiv.innerHTML;
          }
          popup.appendChild(citeDiv);
        });
        return popup.innerHTML;
      });
    }
  }
});
</script>
</div> <!-- /content -->




</body></html>